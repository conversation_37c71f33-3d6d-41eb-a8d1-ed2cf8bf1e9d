<template>
  <div class="relative w-full h-full bg-gradient-to-br from-blue-100 to-green-100 overflow-hidden">
    <!-- 模拟地图背景 -->
    <div class="absolute inset-0 opacity-20">
      <svg viewBox="0 0 400 300" class="w-full h-full">
        <!-- 模拟道路 -->
        <path d="M0,150 Q100,100 200,150 T400,150" stroke="#666" stroke-width="3" fill="none" />
        <path d="M200,0 Q150,100 200,200 T200,300" stroke="#666" stroke-width="2" fill="none" />
        
        <!-- 模拟建筑 -->
        <rect x="50" y="80" width="30" height="40" fill="#ddd" />
        <rect x="120" y="60" width="25" height="50" fill="#ddd" />
        <rect x="280" y="90" width="35" height="35" fill="#ddd" />
        <rect x="320" y="70" width="20" height="60" fill="#ddd" />
        
        <!-- 模拟公园 -->
        <circle cx="150" cy="200" r="25" fill="#90EE90" opacity="0.6" />
        <circle cx="300" cy="180" r="20" fill="#90EE90" opacity="0.6" />
      </svg>
    </div>

    <!-- 地图标记点 -->
    <div 
      v-for="marker in markers" 
      :key="marker.id"
      class="absolute transform -translate-x-1/2 -translate-y-full cursor-pointer transition-transform hover:scale-110"
      :style="{ 
        left: marker.x + '%', 
        top: marker.y + '%' 
      }"
      @click="$emit('markerClick', marker)"
    >
      <div class="relative">
        <!-- 标记图标 -->
        <div class="w-8 h-8 bg-red-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-sm font-bold">
          📍
        </div>
        <!-- 标记标签 -->
        <div class="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-white px-2 py-1 rounded shadow text-xs whitespace-nowrap">
          {{ marker.title }}
        </div>
      </div>
    </div>

    <!-- 当前位置标记 -->
    <div 
      v-if="currentLocation"
      class="absolute transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
      :style="{ 
        left: currentLocation.x + '%', 
        top: currentLocation.y + '%' 
      }"
    >
      <div class="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg"></div>
      <div class="absolute inset-0 w-4 h-4 bg-blue-500 rounded-full animate-ping opacity-30"></div>
    </div>

    <!-- 地图控制按钮 -->
    <div class="absolute top-4 right-4 flex flex-col space-y-2">
      <button 
        @click="$emit('zoomIn')"
        class="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
      >
        ➕
      </button>
      <button 
        @click="$emit('zoomOut')"
        class="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
      >
        ➖
      </button>
      <button 
        @click="$emit('toggleMapType')"
        class="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
      >
        🛰️
      </button>
      <button 
        @click="$emit('getCurrentLocation')"
        class="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
        :class="{ 'animate-spin': isLocating }"
      >
        📍
      </button>
    </div>

    <!-- 缩放级别指示器 -->
    <div class="absolute bottom-4 left-4 bg-white px-3 py-1 rounded-lg shadow text-sm">
      缩放: {{ zoomLevel }}
    </div>

    <!-- 地图类型指示器 -->
    <div class="absolute bottom-4 right-4 bg-white px-3 py-1 rounded-lg shadow text-sm">
      {{ mapType === 'satellite' ? '卫星' : '标准' }}
    </div>

    <!-- 点击添加标记的提示 -->
    <div 
      v-if="showAddHint"
      class="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center"
      @click="$emit('mapClick', $event)"
    >
      <div class="bg-white px-4 py-2 rounded-lg shadow-lg text-center">
        <div class="text-lg mb-2">📍</div>
        <div class="text-sm">点击地图添加新足迹</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  markers: {
    type: Array,
    default: () => []
  },
  currentLocation: {
    type: Object,
    default: null
  },
  isLocating: {
    type: Boolean,
    default: false
  },
  showAddHint: {
    type: Boolean,
    default: false
  },
  zoom: {
    type: Number,
    default: 10
  },
  mapType: {
    type: String,
    default: 'normal'
  }
})

// Emits
defineEmits(['markerClick', 'mapClick', 'zoomIn', 'zoomOut', 'toggleMapType', 'getCurrentLocation'])

// 计算属性
const zoomLevel = computed(() => props.zoom)
</script>

<style scoped>
/* 自定义动画 */
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>

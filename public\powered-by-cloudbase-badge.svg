<svg width="160" height="32" viewBox="0 0 160 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d2d2d;stop-opacity:1" />
    </linearGradient>
    
    <!-- Logo渐变 -->
    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#67E9E9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2BCCCC;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4896FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0052D9;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1"/>
      <feOffset dx="0" dy="1" result="offset"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge> 
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- 主背景 -->
  <rect width="160" height="32" rx="6" fill="url(#bgGradient)" stroke="#333" stroke-width="0.5"/>
  
  <!-- 内部高光 -->
  <rect x="0.5" y="0.5" width="159" height="31" rx="5.5" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/>
  
  <!-- CloudBase Logo -->
  <g transform="translate(8, 8) scale(0.44)">
    <!-- 左侧图形 -->
    <path fill-rule="evenodd" clip-rule="evenodd" 
          d="M-0.00195272 13.364C-0.0021879 13.0869 0.100481 12.8097 0.306053 12.5947L8.64438 4.25689C8.74869 4.15259 8.89015 4.09399 9.03766 4.09399H19.6942C20.1894 4.09399 20.4377 4.69236 20.088 5.04296L11.7889 13.364H-0.00195272Z" 
          fill="url(#logoGradient1)"/>
    <path fill-rule="evenodd" clip-rule="evenodd" 
          d="M-0.00195272 13.366C-0.00218796 13.6431 0.100474 13.9203 0.306033 14.1353L8.64438 22.4731C8.74869 22.5774 8.89015 22.636 9.03766 22.636H19.6942C20.1894 22.636 20.4377 22.0376 20.088 21.687L11.7889 13.366H-0.00195272Z" 
          fill="url(#logoGradient1)"/>
    
    <!-- 右侧图形 -->
    <path fill-rule="evenodd" clip-rule="evenodd" 
          d="M35.999 22.2539C35.9993 21.9781 35.8977 21.7023 35.6942 21.4877L27.3527 13.1468C27.2484 13.0425 27.1069 12.9839 26.9594 12.9839H16.3029C15.8077 12.9839 15.5594 13.5823 15.9091 13.9328L24.2081 22.2539H35.999Z" 
          fill="url(#logoGradient2)"/>
    <path fill-rule="evenodd" clip-rule="evenodd" 
          d="M35.999 22.2534C35.9994 22.526 35.9002 22.7987 35.7015 23.0123L27.3527 31.3605C27.2484 31.4648 27.1069 31.5234 26.9594 31.5234H16.3029C15.8077 31.5234 15.5594 30.9251 15.9091 30.5745L24.2081 22.2534H35.999Z" 
          fill="url(#logoGradient2)"/>
  </g>
  
  <!-- "Powered by" 文字 -->
  <text x="32" y="12" font-family="system-ui, -apple-system, sans-serif" font-size="8" font-weight="400" fill="rgba(255,255,255,0.7)">Powered by</text>
  
  <!-- "CloudBase" 文字 -->
  <text x="32" y="24" font-family="system-ui, -apple-system, sans-serif" font-size="11" font-weight="600" fill="url(#textGradient)" filter="url(#shadow)">CloudBase</text>
  
  <!-- 右侧装饰点 -->
  <circle cx="148" cy="16" r="1.5" fill="#67E9E9" opacity="0.6"/>
  <circle cx="152" cy="16" r="1" fill="#4896FF" opacity="0.8"/>
</svg> 
<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <button 
            @click="$router.back()"
            class="btn btn-sm btn-circle btn-ghost"
          >
            ←
          </button>
          <h1 class="text-xl font-bold text-gray-800">👤 个人中心</h1>
        </div>
        <button 
          @click="showSettings = true"
          class="btn btn-sm btn-ghost"
        >
          ⚙️ 设置
        </button>
      </div>
    </header>

    <div class="max-w-4xl mx-auto p-4 space-y-6">
      <!-- 用户信息卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center space-x-4 mb-6">
          <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold">
            {{ userInfo.name?.charAt(0) || 'U' }}
          </div>
          <div class="flex-1">
            <h2 class="text-2xl font-bold text-gray-800">{{ userInfo.name || '旅行者' }}</h2>
            <p class="text-gray-600">{{ userInfo.bio || '热爱旅行，记录美好时光' }}</p>
            <div class="flex items-center space-x-4 mt-2">
              <span class="text-sm text-gray-500">📅 加入于 {{ formatDate(userInfo.joinDate) }}</span>
              <span class="text-sm text-gray-500">🗺️ {{ stats.totalRecords }} 个足迹</span>
            </div>
          </div>
          <button 
            @click="editProfile"
            class="btn btn-sm btn-outline"
          >
            编辑资料
          </button>
        </div>

        <!-- 成就徽章 -->
        <div class="border-t pt-4">
          <h3 class="font-semibold text-gray-800 mb-3">🏆 旅行成就</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div 
              v-for="achievement in achievements" 
              :key="achievement.id"
              class="flex flex-col items-center p-3 bg-gray-50 rounded-lg"
              :class="{ 'bg-yellow-50 border border-yellow-200': achievement.unlocked }"
            >
              <div class="text-2xl mb-1" :class="{ 'grayscale': !achievement.unlocked }">
                {{ achievement.icon }}
              </div>
              <div class="text-xs text-center font-medium">{{ achievement.name }}</div>
              <div class="text-xs text-gray-500 text-center">{{ achievement.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速统计 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl mb-2">🗺️</div>
          <div class="text-xl font-bold text-blue-600">{{ stats.totalRecords }}</div>
          <div class="text-sm text-gray-600">总足迹</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl mb-2">🏙️</div>
          <div class="text-xl font-bold text-green-600">{{ stats.totalCities }}</div>
          <div class="text-sm text-gray-600">访问城市</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl mb-2">📸</div>
          <div class="text-xl font-bold text-purple-600">{{ stats.totalPhotos }}</div>
          <div class="text-sm text-gray-600">照片数量</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-4 text-center">
          <div class="text-2xl mb-2">⭐</div>
          <div class="text-xl font-bold text-orange-600">{{ stats.avgRating?.toFixed(1) || '0.0' }}</div>
          <div class="text-sm text-gray-600">平均评分</div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="font-semibold text-gray-800 mb-4">🛠️ 功能菜单</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <button 
            @click="$router.push('/stats')"
            class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
          >
            <div class="text-2xl">📊</div>
            <div>
              <div class="font-medium">旅行统计</div>
              <div class="text-sm text-gray-600">查看详细的旅行数据分析</div>
            </div>
          </button>
          
          <button 
            @click="exportData"
            class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
          >
            <div class="text-2xl">📤</div>
            <div>
              <div class="font-medium">导出数据</div>
              <div class="text-sm text-gray-600">导出您的旅行记录</div>
            </div>
          </button>
          
          <button 
            @click="shareProfile"
            class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
          >
            <div class="text-2xl">🔗</div>
            <div>
              <div class="font-medium">分享档案</div>
              <div class="text-sm text-gray-600">分享您的旅行档案</div>
            </div>
          </button>
          
          <button 
            @click="backupData"
            class="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
          >
            <div class="text-2xl">💾</div>
            <div>
              <div class="font-medium">数据备份</div>
              <div class="text-sm text-gray-600">备份您的旅行数据</div>
            </div>
          </button>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="font-semibold text-gray-800 mb-4">📝 最近活动</h3>
        <div class="space-y-3">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
          >
            <div class="text-xl">{{ activity.icon }}</div>
            <div class="flex-1">
              <div class="font-medium">{{ activity.title }}</div>
              <div class="text-sm text-gray-600">{{ activity.description }}</div>
            </div>
            <div class="text-xs text-gray-500">{{ formatDate(activity.date, 'MM-DD') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 设置模态框 -->
    <div v-if="showSettings" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">⚙️ 设置</h3>
        
        <div class="space-y-4">
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">🌙 深色模式</span>
              <input 
                v-model="settings.darkMode"
                type="checkbox" 
                class="toggle toggle-primary" 
              />
            </label>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">📍 自动定位</span>
              <input 
                v-model="settings.autoLocation"
                type="checkbox" 
                class="toggle toggle-primary" 
              />
            </label>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">🔔 推送通知</span>
              <input 
                v-model="settings.notifications"
                type="checkbox" 
                class="toggle toggle-primary" 
              />
            </label>
          </div>
          
          <div class="form-control">
            <label class="label cursor-pointer">
              <span class="label-text">🌐 公开档案</span>
              <input 
                v-model="settings.publicProfile"
                type="checkbox" 
                class="toggle toggle-primary" 
              />
            </label>
          </div>
        </div>
        
        <div class="modal-action">
          <button 
            @click="showSettings = false"
            class="btn btn-ghost"
          >
            取消
          </button>
          <button 
            @click="saveSettings"
            class="btn btn-primary"
          >
            保存
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑资料模态框 -->
    <div v-if="showEditProfile" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">✏️ 编辑资料</h3>
        
        <div class="space-y-4">
          <div class="form-control">
            <label class="label">
              <span class="label-text">姓名</span>
            </label>
            <input 
              v-model="editForm.name"
              type="text" 
              placeholder="输入您的姓名" 
              class="input input-bordered"
            />
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">个人简介</span>
            </label>
            <textarea 
              v-model="editForm.bio"
              placeholder="介绍一下自己..." 
              class="textarea textarea-bordered h-24"
            ></textarea>
          </div>
          
          <div class="form-control">
            <label class="label">
              <span class="label-text">所在城市</span>
            </label>
            <input 
              v-model="editForm.city"
              type="text" 
              placeholder="您的所在城市" 
              class="input input-bordered"
            />
          </div>
        </div>
        
        <div class="modal-action">
          <button 
            @click="showEditProfile = false"
            class="btn btn-ghost"
          >
            取消
          </button>
          <button 
            @click="saveProfile"
            class="btn btn-primary"
            :class="{ 'loading': isSaving }"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { userStats } from '../utils/database.js'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const userInfo = ref({
  name: '旅行者',
  bio: '热爱旅行，记录美好时光',
  city: '北京',
  joinDate: new Date('2024-01-01')
})

const stats = ref({
  totalRecords: 0,
  totalCities: 0,
  totalPhotos: 0,
  avgRating: 0
})

const achievements = ref([
  { id: 1, name: '初次旅行', description: '添加第一个足迹', icon: '🎯', unlocked: true },
  { id: 2, name: '城市探索者', description: '访问5个不同城市', icon: '🏙️', unlocked: true },
  { id: 3, name: '摄影达人', description: '上传50张照片', icon: '📸', unlocked: false },
  { id: 4, name: '旅行专家', description: '记录100个足迹', icon: '🗺️', unlocked: false },
  { id: 5, name: '评分大师', description: '所有记录平均4星以上', icon: '⭐', unlocked: true },
  { id: 6, name: '分享达人', description: '分享10次旅行', icon: '🔗', unlocked: false },
  { id: 7, name: '长途旅行者', description: '总里程超过1000公里', icon: '🛣️', unlocked: false },
  { id: 8, name: '年度旅行者', description: '一年内记录50个足迹', icon: '🏆', unlocked: false }
])

const recentActivities = ref([
  { id: 1, icon: '📍', title: '添加了新足迹', description: '北京故宫', date: new Date() },
  { id: 2, icon: '📸', title: '上传了照片', description: '长城美景', date: new Date(Date.now() - 86400000) },
  { id: 3, icon: '⭐', title: '评分了地点', description: '天安门广场 5星', date: new Date(Date.now() - 172800000) }
])

const showSettings = ref(false)
const showEditProfile = ref(false)
const isSaving = ref(false)

const settings = ref({
  darkMode: false,
  autoLocation: true,
  notifications: true,
  publicProfile: false
})

const editForm = ref({
  name: '',
  bio: '',
  city: ''
})

// 方法
const loadUserData = async () => {
  try {
    const userId = 'current-user-id' // 需要获取真实用户ID
    const userStatsData = await userStats.getUserStats(userId)
    stats.value = userStatsData
    
    // 计算平均评分
    // 这里需要从数据库获取真实数据
    stats.value.avgRating = 4.2
    stats.value.totalPhotos = 156
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

const editProfile = () => {
  editForm.value = { ...userInfo.value }
  showEditProfile.value = true
}

const saveProfile = async () => {
  isSaving.value = true
  try {
    // 这里应该调用API保存用户信息
    userInfo.value = { ...editForm.value }
    showEditProfile.value = false
  } catch (error) {
    console.error('保存资料失败:', error)
  } finally {
    isSaving.value = false
  }
}

const saveSettings = async () => {
  try {
    // 这里应该调用API保存设置
    console.log('保存设置:', settings.value)
    showSettings.value = false
  } catch (error) {
    console.error('保存设置失败:', error)
  }
}

const exportData = () => {
  // 实现数据导出功能
  console.log('导出数据')
  alert('数据导出功能开发中...')
}

const shareProfile = () => {
  // 实现档案分享功能
  if (navigator.share) {
    navigator.share({
      title: `${userInfo.value.name}的旅行档案`,
      text: `我已经记录了${stats.value.totalRecords}个旅行足迹，快来看看吧！`,
      url: window.location.href
    })
  } else {
    navigator.clipboard.writeText(window.location.href)
    alert('档案链接已复制到剪贴板')
  }
}

const backupData = () => {
  // 实现数据备份功能
  console.log('备份数据')
  alert('数据备份功能开发中...')
}

const formatDate = (date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}

// 生命周期
onMounted(() => {
  loadUserData()
})
</script>

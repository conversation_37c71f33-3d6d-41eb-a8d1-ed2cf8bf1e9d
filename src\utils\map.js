import AMapLoader from '@amap/amap-jsapi-loader'

// 高德地图配置
const MAP_CONFIG = {
  key: 'demo-key', // 演示用，实际使用需要申请真实API Key
  version: '2.0',
  plugins: [
    'AMap.Scale',
    'AMap.ToolBar',
    'AMap.Geolocation',
    'AMap.PlaceSearch',
    'AMap.Geocoder'
  ]
}

let AMap = null

/**
 * 初始化高德地图
 */
export async function initAMap() {
  if (AMap) return AMap
  
  try {
    AMap = await AMapLoader.load(MAP_CONFIG)
    return AMap
  } catch (error) {
    console.error('地图加载失败:', error)
    throw error
  }
}

/**
 * 创建地图实例
 */
export function createMap(container, options = {}) {
  if (!AMap) {
    throw new Error('地图未初始化，请先调用 initAMap()')
  }
  
  const defaultOptions = {
    zoom: 10,
    center: [116.397428, 39.90923], // 北京天安门
    viewMode: '3D',
    pitch: 0,
    rotation: 0,
    showLabel: true,
    ...options
  }
  
  return new AMap.Map(container, defaultOptions)
}

/**
 * 添加标记点
 */
export function addMarker(map, options = {}) {
  if (!AMap || !map) return null
  
  const defaultOptions = {
    position: [116.397428, 39.90923],
    title: '标记点',
    ...options
  }
  
  const marker = new AMap.Marker(defaultOptions)
  map.add(marker)
  
  return marker
}

/**
 * 地理编码 - 地址转坐标
 */
export function geocode(address) {
  return new Promise((resolve, reject) => {
    if (!AMap) {
      reject(new Error('地图未初始化'))
      return
    }
    
    const geocoder = new AMap.Geocoder()
    geocoder.getLocation(address, (status, result) => {
      if (status === 'complete' && result.geocodes.length) {
        resolve(result.geocodes[0])
      } else {
        reject(new Error('地理编码失败'))
      }
    })
  })
}

/**
 * 逆地理编码 - 坐标转地址
 */
export function regeocode(lnglat) {
  return new Promise((resolve, reject) => {
    if (!AMap) {
      reject(new Error('地图未初始化'))
      return
    }
    
    const geocoder = new AMap.Geocoder()
    geocoder.getAddress(lnglat, (status, result) => {
      if (status === 'complete' && result.regeocode) {
        resolve(result.regeocode)
      } else {
        reject(new Error('逆地理编码失败'))
      }
    })
  })
}

/**
 * 获取当前位置
 */
export function getCurrentPosition(map) {
  return new Promise((resolve, reject) => {
    if (!AMap || !map) {
      reject(new Error('地图未初始化'))
      return
    }
    
    const geolocation = new AMap.Geolocation({
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0,
      convert: true,
      showButton: true,
      buttonPosition: 'LB',
      buttonOffset: new AMap.Pixel(10, 20),
      showMarker: true,
      showCircle: true,
      panToLocation: true,
      zoomToAccuracy: true
    })
    
    geolocation.getCurrentPosition((status, result) => {
      if (status === 'complete') {
        resolve(result)
      } else {
        reject(new Error('定位失败'))
      }
    })
    
    map.addControl(geolocation)
  })
}

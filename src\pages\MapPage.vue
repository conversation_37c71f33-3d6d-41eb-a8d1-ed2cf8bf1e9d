<template>
  <div class="h-screen flex flex-col bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <h1 class="text-xl font-bold text-gray-800">🗺️ 旅行地图</h1>
        <div class="badge badge-primary badge-sm">{{ totalRecords }} 个足迹</div>
      </div>
      <div class="flex items-center space-x-2">
        <button 
          @click="getCurrentLocation"
          class="btn btn-sm btn-circle btn-ghost"
          :class="{ 'loading': isLocating }"
        >
          📍
        </button>
        <button 
          @click="showAddModal = true"
          class="btn btn-sm btn-primary"
        >
          ➕ 添加足迹
        </button>
      </div>
    </header>

    <!-- 地图容器 -->
    <div class="flex-1 relative">
      <MockMap
        :markers="mapMarkers"
        :current-location="currentLocation"
        :is-locating="isLocating"
        :show-add-hint="showAddHint"
        :zoom="mapZoom"
        :map-type="mapType"
        @marker-click="onMarkerClick"
        @map-click="onMapClick"
        @zoom-in="zoomIn"
        @zoom-out="zoomOut"
        @toggle-map-type="toggleMapType"
        @get-current-location="getCurrentLocation"
      />

      <!-- 底部信息面板 -->
      <div 
        v-if="selectedRecord"
        class="absolute bottom-0 left-0 right-0 bg-white shadow-lg rounded-t-xl p-4 transform transition-transform duration-300"
        :class="{ 'translate-y-full': !showInfoPanel }"
      >
        <div class="flex items-start justify-between mb-3">
          <div>
            <h3 class="font-bold text-lg">{{ selectedRecord.title }}</h3>
            <p class="text-gray-600 text-sm">{{ selectedRecord.location?.address }}</p>
          </div>
          <button 
            @click="closeInfoPanel"
            class="btn btn-sm btn-circle btn-ghost"
          >
            ✕
          </button>
        </div>
        
        <div class="flex items-center space-x-4 mb-3">
          <span class="text-sm text-gray-500">
            📅 {{ formatDate(selectedRecord.visitDate) }}
          </span>
          <span class="text-sm text-gray-500">
            ⭐ {{ selectedRecord.rating }}/5
          </span>
        </div>
        
        <p class="text-gray-700 mb-4 line-clamp-3">{{ selectedRecord.content }}</p>
        
        <div class="flex justify-between">
          <button 
            @click="viewRecordDetail"
            class="btn btn-sm btn-primary"
          >
            查看详情
          </button>
          <div class="flex space-x-2">
            <button 
              @click="editRecord"
              class="btn btn-sm btn-ghost"
            >
              ✏️ 编辑
            </button>
            <button 
              @click="shareRecord"
              class="btn btn-sm btn-ghost"
            >
              🔗 分享
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加足迹模态框 -->
    <div v-if="showAddModal" class="modal modal-open">
      <div class="modal-box">
        <h3 class="font-bold text-lg mb-4">📍 添加新足迹</h3>
        
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">地点名称</span>
          </label>
          <input 
            v-model="newRecord.title"
            type="text" 
            placeholder="输入地点名称..." 
            class="input input-bordered"
          />
        </div>
        
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">详细地址</span>
          </label>
          <input 
            v-model="newRecord.address"
            type="text" 
            placeholder="输入详细地址..." 
            class="input input-bordered"
          />
        </div>
        
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">访问日期</span>
          </label>
          <input 
            v-model="newRecord.visitDate"
            type="date" 
            class="input input-bordered"
          />
        </div>
        
        <div class="form-control mb-4">
          <label class="label">
            <span class="label-text">评分</span>
          </label>
          <div class="rating">
            <input 
              v-for="i in 5" 
              :key="i"
              v-model="newRecord.rating"
              :value="i"
              type="radio" 
              class="mask mask-star-2 bg-orange-400" 
            />
          </div>
        </div>
        
        <div class="modal-action">
          <button 
            @click="showAddModal = false"
            class="btn btn-ghost"
          >
            取消
          </button>
          <button 
            @click="addNewRecord"
            class="btn btn-primary"
            :class="{ 'loading': isAdding }"
          >
            添加
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import MockMap from '../components/MockMap.vue'
import { travelRecords } from '../utils/database.js'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const totalRecords = ref(0)
const isLocating = ref(false)
const showAddModal = ref(false)
const showInfoPanel = ref(false)
const selectedRecord = ref(null)
const isAdding = ref(false)
const showAddHint = ref(false)
const mapZoom = ref(10)
const mapType = ref('normal')
const currentLocation = ref(null)
const mapMarkers = ref([
  // 示例标记点
  { id: 1, x: 30, y: 40, title: '北京故宫', record: { _id: '1', title: '北京故宫', rating: 5 } },
  { id: 2, x: 60, y: 60, title: '上海外滩', record: { _id: '2', title: '上海外滩', rating: 4 } },
  { id: 3, x: 80, y: 30, title: '杭州西湖', record: { _id: '3', title: '杭州西湖', rating: 5 } }
])

// 新记录表单数据
const newRecord = ref({
  title: '',
  address: '',
  visitDate: dayjs().format('YYYY-MM-DD'),
  rating: 5,
  content: ''
})

// 地图相关方法
const loadTravelRecords = async () => {
  try {
    // 这里需要获取当前用户ID，暂时使用固定值
    const userId = 'current-user-id'
    const result = await travelRecords.getUserRecords(userId, 100)

    totalRecords.value = result.data.length

    // 更新地图标记（这里使用示例数据）
    // 实际项目中会根据数据库记录生成标记
    console.log('加载到的记录:', result.data)
  } catch (error) {
    console.error('加载旅行记录失败:', error)
  }
}

const onMapClick = (e) => {
  console.log('地图点击事件:', e)
  // 可以在这里添加快速添加记录的功能
  showAddModal.value = true
}

const onMarkerClick = (marker) => {
  console.log('标记点击:', marker)
  selectedRecord.value = marker.record
  showInfoPanel.value = true
}

// 地图控制方法
const getCurrentLocation = async () => {
  isLocating.value = true
  try {
    // 模拟定位
    setTimeout(() => {
      currentLocation.value = { x: 50, y: 50 }
      isLocating.value = false
    }, 1000)
  } catch (error) {
    console.error('定位失败:', error)
    isLocating.value = false
  }
}

const toggleMapType = () => {
  mapType.value = mapType.value === 'normal' ? 'satellite' : 'normal'
}

const zoomIn = () => {
  if (mapZoom.value < 18) {
    mapZoom.value++
  }
}

const zoomOut = () => {
  if (mapZoom.value > 3) {
    mapZoom.value--
  }
}

// 记录相关方法
const addNewRecord = async () => {
  if (!newRecord.value.title || !newRecord.value.address) {
    alert('请填写完整信息')
    return
  }

  isAdding.value = true
  try {
    const recordData = {
      title: newRecord.value.title,
      content: newRecord.value.content || '暂无描述',
      visitDate: new Date(newRecord.value.visitDate),
      rating: newRecord.value.rating,
      location: {
        address: newRecord.value.address,
        coordinates: [116.397428, 39.90923], // 示例坐标
        city: '北京',
        province: '北京市'
      }
    }

    await travelRecords.create(recordData)

    // 添加到地图标记
    const newMarker = {
      id: Date.now(),
      x: Math.random() * 80 + 10,
      y: Math.random() * 60 + 20,
      title: newRecord.value.title,
      record: { ...recordData, _id: Date.now().toString() }
    }
    mapMarkers.value.push(newMarker)

    // 重新加载数据
    await loadTravelRecords()

    // 重置表单
    newRecord.value = {
      title: '',
      address: '',
      visitDate: dayjs().format('YYYY-MM-DD'),
      rating: 5,
      content: ''
    }

    showAddModal.value = false
    alert('添加成功！')
  } catch (error) {
    console.error('添加记录失败:', error)
    alert('添加失败，请重试')
  } finally {
    isAdding.value = false
  }
}

const viewRecordDetail = () => {
  if (selectedRecord.value) {
    router.push(`/record/${selectedRecord.value._id}`)
  }
}

const editRecord = () => {
  if (selectedRecord.value) {
    router.push(`/record/${selectedRecord.value._id}/edit`)
  }
}

const shareRecord = () => {
  if (selectedRecord.value) {
    // 实现分享功能
    console.log('分享记录:', selectedRecord.value)
  }
}

const closeInfoPanel = () => {
  showInfoPanel.value = false
  selectedRecord.value = null
}

// 工具方法
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 生命周期
onMounted(() => {
  loadTravelRecords()
  totalRecords.value = mapMarkers.value.length
})
</script>

<style scoped>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>

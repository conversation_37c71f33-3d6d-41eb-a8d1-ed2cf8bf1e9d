{"version": "2.0", "envId": "w12-1g8r8jq7b8f4acf4", "$schema": "https://framework-1258016615.tcloudbaseapp.com/schema/latest.json", "framework": {"name": "travel-map-diary", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"outputPath": "dist", "buildCommand": "npm run build", "cloudPath": "/travel-map"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "travelAPI", "config": {"timeout": 30, "runtime": "Nodejs18.15"}}]}}}}}
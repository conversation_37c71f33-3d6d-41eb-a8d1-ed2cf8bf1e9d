const cloudbase = require('@cloudbase/node-sdk')

// 初始化云开发
const app = cloudbase.init({
  env: cloudbase.SYMBOL_CURRENT_ENV
})

const db = app.database()

/**
 * 旅行记录API云函数
 * 处理旅行记录的增删改查操作
 */
exports.main = async (event, context) => {
  const { action, data } = event
  
  try {
    switch (action) {
      case 'createRecord':
        return await createRecord(data)
      case 'getUserRecords':
        return await getUserRecords(data)
      case 'getRecordById':
        return await getRecordById(data)
      case 'updateRecord':
        return await updateRecord(data)
      case 'deleteRecord':
        return await deleteRecord(data)
      case 'getNearbyRecords':
        return await getNearbyRecords(data)
      case 'getUserStats':
        return await getUserStats(data)
      case 'calculateTotalDistance':
        return await calculateTotalDistance(data)
      default:
        return {
          success: false,
          error: '未知操作类型'
        }
    }
  } catch (error) {
    console.error('云函数执行错误:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 创建旅行记录
 */
async function createRecord(data) {
  const { userId, title, content, visitDate, rating, location, photos, tags, weather, expenses, companions } = data
  
  const record = {
    userId,
    title,
    content,
    visitDate: new Date(visitDate),
    rating,
    location,
    photos: photos || [],
    tags: tags || [],
    weather,
    expenses: expenses || [],
    companions: companions || [],
    createTime: new Date(),
    updateTime: new Date()
  }
  
  const result = await db.collection('travel_records').add(record)
  
  return {
    success: true,
    data: result
  }
}

/**
 * 获取用户的旅行记录
 */
async function getUserRecords(data) {
  const { userId, limit = 20, offset = 0, sortBy = 'createTime', sortOrder = 'desc' } = data
  
  const result = await db.collection('travel_records')
    .where({
      userId: userId
    })
    .orderBy(sortBy, sortOrder)
    .limit(limit)
    .skip(offset)
    .get()
  
  return {
    success: true,
    data: result.data,
    total: result.data.length
  }
}

/**
 * 根据ID获取旅行记录详情
 */
async function getRecordById(data) {
  const { recordId } = data
  
  const result = await db.collection('travel_records')
    .doc(recordId)
    .get()
  
  if (result.data.length === 0) {
    return {
      success: false,
      error: '记录不存在'
    }
  }
  
  return {
    success: true,
    data: result.data[0]
  }
}

/**
 * 更新旅行记录
 */
async function updateRecord(data) {
  const { recordId, updates } = data
  
  const result = await db.collection('travel_records')
    .doc(recordId)
    .update({
      ...updates,
      updateTime: new Date()
    })
  
  return {
    success: true,
    data: result
  }
}

/**
 * 删除旅行记录
 */
async function deleteRecord(data) {
  const { recordId } = data
  
  const result = await db.collection('travel_records')
    .doc(recordId)
    .remove()
  
  return {
    success: true,
    data: result
  }
}

/**
 * 获取附近的旅行记录
 */
async function getNearbyRecords(data) {
  const { longitude, latitude, radius = 5000, limit = 20 } = data
  
  const result = await db.collection('travel_records')
    .where({
      location: db.command.geoNear({
        geometry: db.Geo.Point(longitude, latitude),
        maxDistance: radius
      })
    })
    .limit(limit)
    .get()
  
  return {
    success: true,
    data: result.data
  }
}

/**
 * 获取用户统计数据
 */
async function getUserStats(data) {
  const { userId } = data
  
  // 获取总记录数
  const totalRecordsResult = await db.collection('travel_records')
    .where({ userId })
    .count()
  
  // 获取所有记录用于统计
  const allRecordsResult = await db.collection('travel_records')
    .where({ userId })
    .get()
  
  const records = allRecordsResult.data
  
  // 计算访问过的城市数量
  const uniqueCities = new Set(
    records.map(record => record.location?.city).filter(Boolean)
  )
  
  // 计算平均评分
  const avgRating = records.length > 0 
    ? records.reduce((sum, record) => sum + record.rating, 0) / records.length 
    : 0
  
  // 计算照片总数
  const totalPhotos = records.reduce((total, record) => {
    return total + (record.photos?.length || 0)
  }, 0)
  
  return {
    success: true,
    data: {
      totalRecords: totalRecordsResult.total,
      totalCities: uniqueCities.size,
      avgRating,
      totalPhotos,
      lastUpdateTime: new Date()
    }
  }
}

/**
 * 计算总里程（简化版本）
 */
async function calculateTotalDistance(data) {
  const { userId } = data
  
  const result = await db.collection('travel_records')
    .where({ userId })
    .orderBy('visitDate', 'asc')
    .get()
  
  const records = result.data
  let totalDistance = 0
  
  // 简化计算：相邻记录之间的直线距离
  for (let i = 1; i < records.length; i++) {
    const prev = records[i - 1]
    const curr = records[i]
    
    if (prev.location?.coordinates && curr.location?.coordinates) {
      const distance = calculateDistance(
        prev.location.coordinates[1], prev.location.coordinates[0],
        curr.location.coordinates[1], curr.location.coordinates[0]
      )
      totalDistance += distance
    }
  }
  
  return {
    success: true,
    data: {
      distance: Math.round(totalDistance)
    }
  }
}

/**
 * 计算两点间距离（单位：米）
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3 // 地球半径（米）
  const φ1 = lat1 * Math.PI / 180
  const φ2 = lat2 * Math.PI / 180
  const Δφ = (lat2 - lat1) * Math.PI / 180
  const Δλ = (lon2 - lon1) * Math.PI / 180
  
  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  
  return R * c
}

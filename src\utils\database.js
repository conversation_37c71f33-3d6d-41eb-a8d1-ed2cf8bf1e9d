import { app, ensureLogin } from './cloudbase.js'

/**
 * 获取数据库实例
 */
export async function getDB() {
  await ensureLogin()
  return app.database()
}

/**
 * 旅行记录相关操作
 */
export const travelRecords = {
  // 创建旅行记录
  async create(record) {
    try {
      const result = await app.callFunction({
        name: 'hello', // 暂时使用hello函数，后续会创建专门的API函数
        data: {
          action: 'createRecord',
          data: {
            ...record,
            userId: 'current-user-id' // 需要获取真实用户ID
          }
        }
      })
      return result.result
    } catch (error) {
      console.error('创建记录失败:', error)
      // 降级到直接数据库操作
      const db = await getDB()
      const result = await db.collection('travel_records').add({
        ...record,
        userId: 'current-user-id',
        createTime: new Date(),
        updateTime: new Date()
      })
      return { success: true, data: result }
    }
  },

  // 获取用户的旅行记录列表
  async getUserRecords(userId, limit = 20, offset = 0) {
    try {
      // 直接使用数据库查询，因为权限设置为所有人可读
      const db = await getDB()
      const result = await db.collection('travel_records')
        .where({
          userId: userId || 'current-user-id'
        })
        .orderBy('createTime', 'desc')
        .limit(limit)
        .skip(offset)
        .get()
      return result
    } catch (error) {
      console.error('获取记录失败:', error)
      return { data: [] }
    }
  },

  // 根据ID获取旅行记录详情
  async getById(recordId) {
    try {
      const db = await getDB()
      const result = await db.collection('travel_records')
        .doc(recordId)
        .get()
      return result.data[0]
    } catch (error) {
      console.error('获取记录详情失败:', error)
      return null
    }
  },

  // 更新旅行记录
  async update(recordId, updates) {
    try {
      const db = await getDB()
      const result = await db.collection('travel_records')
        .doc(recordId)
        .update({
          ...updates,
          updateTime: new Date()
        })
      return result
    } catch (error) {
      console.error('更新记录失败:', error)
      throw error
    }
  },

  // 删除旅行记录
  async delete(recordId) {
    try {
      const db = await getDB()
      const result = await db.collection('travel_records')
        .doc(recordId)
        .remove()
      return result
    } catch (error) {
      console.error('删除记录失败:', error)
      throw error
    }
  },

  // 根据地理位置查询附近的记录
  async getNearbyRecords(longitude, latitude, radius = 5000) {
    try {
      const db = await getDB()
      const result = await db.collection('travel_records')
        .where({
          location: db.command.geoNear({
            geometry: db.Geo.Point(longitude, latitude),
            maxDistance: radius
          })
        })
        .get()
      return result
    } catch (error) {
      console.error('查询附近记录失败:', error)
      return { data: [] }
    }
  }
}

/**
 * 用户统计数据相关操作
 */
export const userStats = {
  // 获取用户统计数据
  async getUserStats(userId) {
    const db = await getDB()
    
    // 获取总记录数
    const totalRecords = await db.collection('travel_records')
      .where({ userId })
      .count()
    
    // 获取访问过的城市数量
    const citiesResult = await db.collection('travel_records')
      .where({ userId })
      .field({ 'location.city': true })
      .get()
    
    const uniqueCities = new Set(
      citiesResult.data.map(record => record.location?.city).filter(Boolean)
    )
    
    // 获取总里程（这里需要通过云函数计算）
    const totalDistance = await app.callFunction({
      name: 'calculateTotalDistance',
      data: { userId }
    })
    
    return {
      totalRecords: totalRecords.total,
      totalCities: uniqueCities.size,
      totalDistance: totalDistance.result?.distance || 0,
      lastUpdateTime: new Date()
    }
  }
}

/**
 * 分享记录相关操作
 */
export const shareRecords = {
  // 创建分享记录
  async create(shareData) {
    const db = await getDB()
    const result = await db.collection('share_records').add({
      ...shareData,
      createTime: new Date(),
      viewCount: 0,
      likeCount: 0
    })
    return result
  },

  // 获取公开分享的记录
  async getPublicShares(limit = 20, offset = 0) {
    const db = await getDB()
    const result = await db.collection('share_records')
      .where({
        isPublic: true
      })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .skip(offset)
      .get()
    return result
  },

  // 增加分享记录的查看次数
  async incrementViewCount(shareId) {
    const db = await getDB()
    const result = await db.collection('share_records')
      .doc(shareId)
      .update({
        viewCount: db.command.inc(1)
      })
    return result
  }
}

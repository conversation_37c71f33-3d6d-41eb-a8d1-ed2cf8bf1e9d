import { app } from './cloudbase.js'

/**
 * 上传文件到云存储
 * @param {File} file 文件对象
 * @param {string} cloudPath 云端路径
 * @returns {Promise} 上传结果
 */
export async function uploadFile(file, cloudPath) {
  try {
    const result = await app.uploadFile({
      cloudPath,
      filePath: file
    })
    return result
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

/**
 * 获取文件下载链接
 * @param {string} fileID 文件ID
 * @returns {Promise} 下载链接
 */
export async function getFileDownloadURL(fileID) {
  try {
    const result = await app.getTempFileURL({
      fileList: [fileID]
    })
    return result.fileList[0]?.tempFileURL
  } catch (error) {
    console.error('获取文件链接失败:', error)
    throw error
  }
}

/**
 * 删除云存储文件
 * @param {string[]} fileIDs 文件ID数组
 * @returns {Promise} 删除结果
 */
export async function deleteFiles(fileIDs) {
  try {
    const result = await app.deleteFile({
      fileList: fileIDs
    })
    return result
  } catch (error) {
    console.error('文件删除失败:', error)
    throw error
  }
}

/**
 * 压缩图片
 * @param {File} file 图片文件
 * @param {number} quality 压缩质量 0-1
 * @param {number} maxWidth 最大宽度
 * @returns {Promise<File>} 压缩后的文件
 */
export function compressImage(file, quality = 0.8, maxWidth = 1200) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)
      
      // 转换为Blob
      canvas.toBlob(resolve, file.type, quality)
    }
    
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 生成唯一的文件路径
 * @param {string} fileName 原文件名
 * @param {string} folder 文件夹名
 * @returns {string} 云端路径
 */
export function generateCloudPath(fileName, folder = 'travel') {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const ext = fileName.split('.').pop()
  return `${folder}/${timestamp}_${randomStr}.${ext}`
}

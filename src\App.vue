<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 主要内容区域 -->
    <main class="pb-16">
      <router-view />
    </main>

    <!-- 底部导航栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50">
      <div class="flex justify-around items-center max-w-md mx-auto">
        <router-link
          to="/"
          class="flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors"
          :class="{ 'text-blue-600 bg-blue-50': $route.path === '/' }"
        >
          <span class="text-xl">🗺️</span>
          <span class="text-xs font-medium">地图</span>
        </router-link>

        <router-link
          to="/stats"
          class="flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors"
          :class="{ 'text-blue-600 bg-blue-50': $route.path === '/stats' }"
        >
          <span class="text-xl">📊</span>
          <span class="text-xs font-medium">统计</span>
        </router-link>

        <router-link
          to="/profile"
          class="flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors"
          :class="{ 'text-blue-600 bg-blue-50': $route.path === '/profile' }"
        >
          <span class="text-xl">👤</span>
          <span class="text-xs font-medium">我的</span>
        </router-link>
      </div>
    </nav>
  </div>
</template>

<script setup>
// 移除Footer组件，使用底部导航栏
</script>
<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <button 
          @click="$router.back()"
          class="btn btn-sm btn-circle btn-ghost"
        >
          ←
        </button>
        <h1 class="text-xl font-bold text-gray-800">旅行详情</h1>
      </div>
      <div class="flex items-center space-x-2">
        <button 
          @click="editRecord"
          class="btn btn-sm btn-ghost"
        >
          ✏️ 编辑
        </button>
        <button 
          @click="shareRecord"
          class="btn btn-sm btn-primary"
        >
          🔗 分享
        </button>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="loading loading-spinner loading-lg"></div>
    </div>

    <!-- 记录详情 -->
    <div v-else-if="record" class="max-w-4xl mx-auto p-4 space-y-6">
      <!-- 标题和基本信息 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">{{ record.title }}</h1>
        
        <div class="flex flex-wrap items-center gap-4 mb-4">
          <div class="flex items-center space-x-2">
            <span class="text-gray-500">📍</span>
            <span class="text-gray-700">{{ record.location?.address }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-gray-500">📅</span>
            <span class="text-gray-700">{{ formatDate(record.visitDate) }}</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-gray-500">⭐</span>
            <div class="rating rating-sm">
              <input 
                v-for="i in 5" 
                :key="i"
                type="radio" 
                class="mask mask-star-2 bg-orange-400" 
                :checked="i <= record.rating"
                disabled
              />
            </div>
            <span class="text-gray-700">{{ record.rating }}/5</span>
          </div>
        </div>

        <!-- 标签 -->
        <div v-if="record.tags?.length" class="flex flex-wrap gap-2 mb-4">
          <span 
            v-for="tag in record.tags" 
            :key="tag"
            class="badge badge-primary badge-outline"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- 照片画廊 -->
      <div v-if="record.photos?.length" class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">📸 照片</h2>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div 
            v-for="(photo, index) in record.photos" 
            :key="index"
            class="aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
            @click="openPhotoViewer(index)"
          >
            <img 
              :src="photo.url" 
              :alt="photo.caption || '旅行照片'"
              class="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>

      <!-- 旅行日记 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">📝 旅行日记</h2>
        <div class="prose max-w-none">
          <p class="text-gray-700 leading-relaxed whitespace-pre-wrap">{{ record.content }}</p>
        </div>
      </div>

      <!-- 地图位置 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">🗺️ 位置</h2>
        <div id="detail-map" class="w-full h-64 rounded-lg overflow-hidden"></div>
      </div>

      <!-- 天气信息 -->
      <div v-if="record.weather" class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">🌤️ 当时天气</h2>
        <div class="flex items-center space-x-4">
          <div class="text-4xl">{{ getWeatherIcon(record.weather.condition) }}</div>
          <div>
            <p class="text-lg font-semibold">{{ record.weather.condition }}</p>
            <p class="text-gray-600">{{ record.weather.temperature }}°C</p>
            <p class="text-gray-600">湿度: {{ record.weather.humidity }}%</p>
          </div>
        </div>
      </div>

      <!-- 花费记录 -->
      <div v-if="record.expenses?.length" class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">💰 花费记录</h2>
        <div class="space-y-3">
          <div 
            v-for="expense in record.expenses" 
            :key="expense.id"
            class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
          >
            <div>
              <span class="font-medium">{{ expense.category }}</span>
              <span v-if="expense.description" class="text-gray-600 ml-2">{{ expense.description }}</span>
            </div>
            <span class="font-semibold text-green-600">¥{{ expense.amount }}</span>
          </div>
          <div class="flex justify-between items-center pt-3 border-t border-gray-200 font-bold">
            <span>总计</span>
            <span class="text-green-600">¥{{ totalExpense }}</span>
          </div>
        </div>
      </div>

      <!-- 同行人员 -->
      <div v-if="record.companions?.length" class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">👥 同行人员</h2>
        <div class="flex flex-wrap gap-3">
          <div 
            v-for="companion in record.companions" 
            :key="companion.id"
            class="flex items-center space-x-2 bg-gray-100 rounded-full px-3 py-1"
          >
            <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
              {{ companion.name.charAt(0) }}
            </div>
            <span class="text-sm">{{ companion.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录不存在 -->
    <div v-else class="flex flex-col items-center justify-center h-64">
      <div class="text-6xl mb-4">😕</div>
      <h2 class="text-xl font-bold text-gray-800 mb-2">记录不存在</h2>
      <p class="text-gray-600 mb-4">该旅行记录可能已被删除或不存在</p>
      <button 
        @click="$router.push('/')"
        class="btn btn-primary"
      >
        返回首页
      </button>
    </div>

    <!-- 照片查看器 -->
    <div v-if="showPhotoViewer" class="modal modal-open">
      <div class="modal-box max-w-4xl">
        <div class="flex justify-between items-center mb-4">
          <h3 class="font-bold text-lg">照片 {{ currentPhotoIndex + 1 }} / {{ record.photos.length }}</h3>
          <button 
            @click="closePhotoViewer"
            class="btn btn-sm btn-circle btn-ghost"
          >
            ✕
          </button>
        </div>
        
        <div class="relative">
          <img 
            :src="record.photos[currentPhotoIndex]?.url" 
            :alt="record.photos[currentPhotoIndex]?.caption"
            class="w-full max-h-96 object-contain rounded-lg"
          />
          
          <!-- 导航按钮 -->
          <button 
            v-if="currentPhotoIndex > 0"
            @click="previousPhoto"
            class="absolute left-2 top-1/2 transform -translate-y-1/2 btn btn-circle btn-sm bg-black bg-opacity-50 text-white border-none"
          >
            ←
          </button>
          <button 
            v-if="currentPhotoIndex < record.photos.length - 1"
            @click="nextPhoto"
            class="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-circle btn-sm bg-black bg-opacity-50 text-white border-none"
          >
            →
          </button>
        </div>
        
        <p v-if="record.photos[currentPhotoIndex]?.caption" class="text-center text-gray-600 mt-4">
          {{ record.photos[currentPhotoIndex].caption }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { travelRecords } from '../utils/database.js'
import { initAMap, createMap, addMarker } from '../utils/map.js'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const record = ref(null)
const showPhotoViewer = ref(false)
const currentPhotoIndex = ref(0)
const detailMap = ref(null)

// 计算属性
const totalExpense = computed(() => {
  if (!record.value?.expenses) return 0
  return record.value.expenses.reduce((total, expense) => total + expense.amount, 0)
})

// 方法
const loadRecord = async () => {
  try {
    loading.value = true
    const recordId = route.params.id
    const result = await travelRecords.getById(recordId)
    record.value = result
    
    // 初始化地图
    if (result?.location?.coordinates) {
      await initDetailMap()
    }
  } catch (error) {
    console.error('加载记录失败:', error)
  } finally {
    loading.value = false
  }
}

const initDetailMap = async () => {
  try {
    await initAMap()
    detailMap.value = createMap('detail-map', {
      zoom: 15,
      center: record.value.location.coordinates
    })
    
    // 添加标记
    addMarker(detailMap.value, {
      position: record.value.location.coordinates,
      title: record.value.title
    })
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
}

const editRecord = () => {
  router.push(`/record/${route.params.id}/edit`)
}

const shareRecord = () => {
  // 实现分享功能
  if (navigator.share) {
    navigator.share({
      title: record.value.title,
      text: record.value.content,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    alert('链接已复制到剪贴板')
  }
}

const openPhotoViewer = (index) => {
  currentPhotoIndex.value = index
  showPhotoViewer.value = true
}

const closePhotoViewer = () => {
  showPhotoViewer.value = false
}

const previousPhoto = () => {
  if (currentPhotoIndex.value > 0) {
    currentPhotoIndex.value--
  }
}

const nextPhoto = () => {
  if (currentPhotoIndex.value < record.value.photos.length - 1) {
    currentPhotoIndex.value++
  }
}

const getWeatherIcon = (condition) => {
  const weatherIcons = {
    '晴': '☀️',
    '多云': '⛅',
    '阴': '☁️',
    '雨': '🌧️',
    '雪': '❄️',
    '雾': '🌫️'
  }
  return weatherIcons[condition] || '🌤️'
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 生命周期
onMounted(() => {
  loadRecord()
})
</script>

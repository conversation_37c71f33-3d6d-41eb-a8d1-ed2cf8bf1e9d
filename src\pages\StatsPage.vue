<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
      <div class="flex items-center space-x-3">
        <button 
          @click="$router.back()"
          class="btn btn-sm btn-circle btn-ghost"
        >
          ←
        </button>
        <h1 class="text-xl font-bold text-gray-800">📊 旅行统计</h1>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="loading loading-spinner loading-lg"></div>
    </div>

    <!-- 统计内容 -->
    <div v-else class="max-w-6xl mx-auto p-4 space-y-6">
      <!-- 总览卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white rounded-xl shadow-sm p-6 text-center">
          <div class="text-3xl mb-2">🗺️</div>
          <div class="text-2xl font-bold text-blue-600">{{ stats.totalRecords }}</div>
          <div class="text-gray-600">总足迹数</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-6 text-center">
          <div class="text-3xl mb-2">🏙️</div>
          <div class="text-2xl font-bold text-green-600">{{ stats.totalCities }}</div>
          <div class="text-gray-600">访问城市</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-6 text-center">
          <div class="text-3xl mb-2">🛣️</div>
          <div class="text-2xl font-bold text-purple-600">{{ formatDistance(stats.totalDistance) }}</div>
          <div class="text-gray-600">总里程</div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm p-6 text-center">
          <div class="text-3xl mb-2">📸</div>
          <div class="text-2xl font-bold text-orange-600">{{ stats.totalPhotos }}</div>
          <div class="text-gray-600">照片数量</div>
        </div>
      </div>

      <!-- 月度统计图表 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">📈 月度足迹趋势</h2>
        <div class="h-64 flex items-end justify-between space-x-2">
          <div 
            v-for="month in monthlyStats" 
            :key="month.month"
            class="flex-1 flex flex-col items-center"
          >
            <div 
              class="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
              :style="{ height: `${(month.count / maxMonthlyCount) * 200}px` }"
              :title="`${month.month}: ${month.count}个足迹`"
            ></div>
            <div class="text-xs text-gray-600 mt-2 transform -rotate-45">{{ month.month }}</div>
          </div>
        </div>
      </div>

      <!-- 城市排行榜 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white rounded-xl shadow-sm p-6">
          <h2 class="text-xl font-bold text-gray-800 mb-4">🏆 最爱城市</h2>
          <div class="space-y-3">
            <div 
              v-for="(city, index) in topCities" 
              :key="city.name"
              class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                  {{ index + 1 }}
                </div>
                <div>
                  <div class="font-semibold">{{ city.name }}</div>
                  <div class="text-sm text-gray-600">{{ city.count }}次访问</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm text-gray-600">平均评分</div>
                <div class="flex items-center">
                  <span class="text-yellow-500">⭐</span>
                  <span class="ml-1 font-semibold">{{ city.avgRating.toFixed(1) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 评分分布 -->
        <div class="bg-white rounded-xl shadow-sm p-6">
          <h2 class="text-xl font-bold text-gray-800 mb-4">⭐ 评分分布</h2>
          <div class="space-y-3">
            <div 
              v-for="rating in ratingDistribution" 
              :key="rating.stars"
              class="flex items-center space-x-3"
            >
              <div class="flex items-center space-x-1 w-16">
                <span>{{ rating.stars }}</span>
                <span class="text-yellow-500">⭐</span>
              </div>
              <div class="flex-1 bg-gray-200 rounded-full h-4 relative">
                <div 
                  class="bg-yellow-500 h-4 rounded-full transition-all duration-300"
                  :style="{ width: `${(rating.count / stats.totalRecords) * 100}%` }"
                ></div>
              </div>
              <div class="text-sm text-gray-600 w-12 text-right">{{ rating.count }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间线 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">📅 旅行时间线</h2>
        <div class="space-y-4">
          <div 
            v-for="record in recentRecords" 
            :key="record._id"
            class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
            @click="viewRecord(record._id)"
          >
            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
              {{ formatDate(record.visitDate, 'DD') }}
            </div>
            <div class="flex-1">
              <div class="font-semibold">{{ record.title }}</div>
              <div class="text-sm text-gray-600">{{ record.location?.address }}</div>
              <div class="text-xs text-gray-500">{{ formatDate(record.visitDate, 'YYYY年MM月') }}</div>
            </div>
            <div class="flex items-center space-x-1">
              <span class="text-yellow-500">⭐</span>
              <span class="text-sm">{{ record.rating }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 年度回顾 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">🎊 {{ currentYear }}年度回顾</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
            <div class="text-2xl mb-2">🏃‍♂️</div>
            <div class="text-lg font-bold">{{ yearStats.mostActiveMonth }}</div>
            <div class="text-sm text-gray-600">最活跃月份</div>
          </div>
          
          <div class="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
            <div class="text-2xl mb-2">🎯</div>
            <div class="text-lg font-bold">{{ yearStats.favoriteCity }}</div>
            <div class="text-sm text-gray-600">最爱城市</div>
          </div>
          
          <div class="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
            <div class="text-2xl mb-2">🌟</div>
            <div class="text-lg font-bold">{{ yearStats.avgRating.toFixed(1) }}</div>
            <div class="text-sm text-gray-600">平均评分</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { travelRecords, userStats } from '../utils/database.js'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(true)
const stats = ref({
  totalRecords: 0,
  totalCities: 0,
  totalDistance: 0,
  totalPhotos: 0
})
const monthlyStats = ref([])
const topCities = ref([])
const ratingDistribution = ref([])
const recentRecords = ref([])
const yearStats = ref({
  mostActiveMonth: '',
  favoriteCity: '',
  avgRating: 0
})

// 计算属性
const currentYear = computed(() => new Date().getFullYear())
const maxMonthlyCount = computed(() => {
  return Math.max(...monthlyStats.value.map(m => m.count), 1)
})

// 方法
const loadStats = async () => {
  try {
    loading.value = true
    const userId = 'current-user-id' // 需要获取真实用户ID
    
    // 获取基础统计数据
    const userStatsData = await userStats.getUserStats(userId)
    stats.value = userStatsData
    
    // 获取用户所有记录用于分析
    const allRecords = await travelRecords.getUserRecords(userId, 1000)
    const records = allRecords.data
    
    // 计算月度统计
    calculateMonthlyStats(records)
    
    // 计算城市排行
    calculateTopCities(records)
    
    // 计算评分分布
    calculateRatingDistribution(records)
    
    // 获取最近记录
    recentRecords.value = records.slice(0, 10)
    
    // 计算年度统计
    calculateYearStats(records)
    
    // 计算照片总数
    stats.value.totalPhotos = records.reduce((total, record) => {
      return total + (record.photos?.length || 0)
    }, 0)
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

const calculateMonthlyStats = (records) => {
  const monthlyData = {}
  
  // 初始化最近12个月
  for (let i = 11; i >= 0; i--) {
    const month = dayjs().subtract(i, 'month').format('YYYY-MM')
    monthlyData[month] = 0
  }
  
  // 统计每月记录数
  records.forEach(record => {
    const month = dayjs(record.visitDate).format('YYYY-MM')
    if (monthlyData.hasOwnProperty(month)) {
      monthlyData[month]++
    }
  })
  
  monthlyStats.value = Object.entries(monthlyData).map(([month, count]) => ({
    month: dayjs(month).format('MM月'),
    count
  }))
}

const calculateTopCities = (records) => {
  const cityData = {}
  
  records.forEach(record => {
    const city = record.location?.city
    if (city) {
      if (!cityData[city]) {
        cityData[city] = {
          name: city,
          count: 0,
          totalRating: 0
        }
      }
      cityData[city].count++
      cityData[city].totalRating += record.rating
    }
  })
  
  topCities.value = Object.values(cityData)
    .map(city => ({
      ...city,
      avgRating: city.totalRating / city.count
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
}

const calculateRatingDistribution = (records) => {
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  
  records.forEach(record => {
    distribution[record.rating]++
  })
  
  ratingDistribution.value = Object.entries(distribution).map(([stars, count]) => ({
    stars: parseInt(stars),
    count
  }))
}

const calculateYearStats = (records) => {
  const currentYearRecords = records.filter(record => 
    dayjs(record.visitDate).year() === currentYear.value
  )
  
  if (currentYearRecords.length === 0) {
    yearStats.value = {
      mostActiveMonth: '暂无数据',
      favoriteCity: '暂无数据',
      avgRating: 0
    }
    return
  }
  
  // 最活跃月份
  const monthCounts = {}
  currentYearRecords.forEach(record => {
    const month = dayjs(record.visitDate).format('MM月')
    monthCounts[month] = (monthCounts[month] || 0) + 1
  })
  
  const mostActiveMonth = Object.entries(monthCounts)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || '暂无数据'
  
  // 最爱城市
  const cityCounts = {}
  currentYearRecords.forEach(record => {
    const city = record.location?.city
    if (city) {
      cityCounts[city] = (cityCounts[city] || 0) + 1
    }
  })
  
  const favoriteCity = Object.entries(cityCounts)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || '暂无数据'
  
  // 平均评分
  const avgRating = currentYearRecords.reduce((sum, record) => sum + record.rating, 0) / currentYearRecords.length
  
  yearStats.value = {
    mostActiveMonth,
    favoriteCity,
    avgRating
  }
}

const formatDistance = (distance) => {
  if (distance < 1000) {
    return `${distance}m`
  }
  return `${(distance / 1000).toFixed(1)}km`
}

const formatDate = (date, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}

const viewRecord = (recordId) => {
  router.push(`/record/${recordId}`)
}

// 生命周期
onMounted(() => {
  loadStats()
})
</script>
